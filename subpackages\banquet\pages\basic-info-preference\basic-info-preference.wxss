page {
  background-color: #F6F6F5;
}

.container {
  padding: 24rpx;
}

/* 信息组样式 */
.info-group {
  margin-bottom: 32rpx;
}

.preference-group {
  margin-bottom: 32rpx;
}

/* 标签区域样式 */
.tag-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0, 0, 0, 0.01);
}

.tag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.tag-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.add-icon {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.custom-tag {
  width: 124rpx;
  height: 56rpx;
  background: #F4F4F6;
  border-radius: 32rpx;
  color: #333;
  border: none;
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 重写 vant cell 样式 */
.van-cell {
  padding: 32rpx 24rpx !important;
  font-size: 32rpx !important;
  background-color: #fff !important;
}

.van-cell__title {
  color: #333 !important;
  font-size: 32rpx !important;
}

.van-cell__value {
  color: #333 !important;
  font-size: 32rpx !important;
}

.van-cell__right-icon {
  color: #999 !important;
}

/* 自定义 cell group 样式 */
.van-cell-group {
  background-color: #fff !important;
  border-radius: 16rpx !important;
  overflow: hidden !important;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0, 0, 0, 0.01) !important;
}

.van-cell:not(:last-child)::after {
  border-bottom: 1rpx solid #f5f5f5 !important;
  left: 24rpx !important;
  right: 24rpx !important;
}

/* Picker 样式 */
.picker-popup {
  border-radius: 32rpx 32rpx 0 0 !important;
}

.picker-container {
  background-color: #fff;
  border-radius: 32rpx 32rpx 0 0;
}

.picker-header {
  padding: 40rpx 32rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #f5f5f5;
}

.picker-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.custom-picker {
  padding: 40rpx 0;
  min-height: 400rpx;
}

.van-picker-column {
  background-color: #fff !important;
}

.van-picker-column__item {
  font-size: 32rpx !important;
  color: #ccc !important;
  line-height: 88rpx !important;
  text-align: center !important;
  transition: all 0.3s ease !important;
}

.van-picker-column__item--selected {
  color: #333 !important;
  font-weight: bold !important;
  font-size: 36rpx !important;
  transform: scale(1.1) !important;
}

/* Picker 滚动区域样式 */
.van-picker__mask {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, transparent 40%, transparent 60%, rgba(255, 255, 255, 0.9) 100%) !important;
}

.van-picker__frame {
  border-top: 2rpx solid #e5e5e5 !important;
  border-bottom: 2rpx solid #e5e5e5 !important;
}

/* 自定义按钮样式 */
.picker-buttons {
  display: flex;
  padding: 32rpx 32rpx 0 40rpx;
  gap: 24rpx;
}

.picker-button {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-button {
  background-color: #79AA6B;
  color: #fff;
}

.button-text {
  font-size: 32rpx;
}



.delete-icon {
  color: #999 !important;
  margin-left: 8rpx;
}