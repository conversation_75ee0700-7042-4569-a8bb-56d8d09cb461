<!-- 功能导航区域组件 -->
<view class="function-nav">
  <!-- 测试显示 -->
  <view style="position: absolute; top: 10rpx; left: 10rpx; background: red; color: white; padding: 10rpx; z-index: 999;">
    组件已加载: {{calculatedMenuPositions.length}}个菜单项
  </view>

  <!-- 背景图片 -->
  <image class="function-nav-bg" src="{{bgImagePath}}" mode="aspectFill"></image>

  <!-- 机器人图片 -->
  <image class="robot" src="{{robotImagePath}}" mode="aspectFit|aspectFill|widthFix" lazy-load="false"></image>
  
  <view class="function-nav-content">
    <!-- 椭圆环形菜单 - 星系旋转版本 -->
    <view
      class="elliptical-menu {{galaxyRotation.isUserDragging ? 'galaxy-rotating' : ''}} {{galaxyRotation.isInertiaActive ? 'inertia-sliding' : ''}} {{isCircleRotating ? 'menu-item-transition' : ''}}"
      bindtouchstart="handleTouchStart" 
      bindtouchmove="handleTouchMove" 
      bindtouchend="handleTouchEnd"
      bindtouchcancel="handleTouchEnd">

      <!-- 太极阴阳背景 -->
      <view class="taiji-background">
        <view class="taiji-container">
          <image class="taiji-image" src="{{taijiImagePath}}" mode="aspectFill"></image>
        </view>
      </view>

      <!-- 五行能量环 -->
      <view class="wuxing-energy-ring">
        <view class="energy-ring energy-ring-wood"></view>
        <view class="energy-ring energy-ring-fire"></view>
        <view class="energy-ring energy-ring-earth"></view>
        <view class="energy-ring energy-ring-metal"></view>
        <view class="energy-ring energy-ring-water"></view>
      </view>

      <!-- 星宿轨道系统 -->
      <view class="constellation-system">
        <view class="orbit-layer orbit-inner">
          <view class="star-particle star-1"></view>
          <view class="star-particle star-2"></view>
          <view class="star-particle star-3"></view>
        </view>
        <view class="orbit-layer orbit-middle">
          <view class="star-particle star-4"></view>
          <view class="star-particle star-5"></view>
          <view class="star-particle star-6"></view>
          <view class="star-particle star-7"></view>
        </view>
        <view class="orbit-layer orbit-outer">
          <view class="star-particle star-8"></view>
          <view class="star-particle star-9"></view>
        </view>
      </view>

      <!-- 能量流转线条 -->
      <view class="energy-flow-system">
        <view class="energy-line energy-line-1" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
        <view class="energy-line energy-line-2" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
        <view class="energy-line energy-line-3" style="display: {{energyFlowActive ? 'block' : 'none'}}"></view>
      </view>

      <!-- 动态渲染菜单项 -->
      <view wx:for="{{calculatedMenuPositions}}" wx:key="index"
        class="menu-item {{currentCircleIndex === item.index ? 'active' : ''}} {{isMenuAnimating ? 'animating' : ''}} {{selectionRippleIndex === item.index ? 'ripple' : ''}} {{forceResetMenu ? 'force-reset' : ''}} {{item.blur > 0.5 ? 'blurred' : ''}}"
        style="left: {{item.x}}%; top: {{item.y}}%; transform: translate(-50%, -50%) scale({{item.scale || 1}}) translateZ(0); opacity: {{item.opacity || 1}};"
        bindtap="handleCircleItemClick" 
        data-index="{{item.index}}" 
        data-type="{{item.name}}">

        <!-- 菜单项五行光晕 -->
        <view class="wuxing-aura wuxing-{{item.wuxingType || 'wood'}}"></view>

        <!-- 菜单图标 -->
        <image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image>

        <!-- 点击涟漪效果 -->
        <view class="ripple-effect" wx:if="{{selectionRippleIndex === item.index}}">
          <view class="ripple-circle ripple-1"></view>
          <view class="ripple-circle ripple-2"></view>
          <view class="ripple-circle ripple-3"></view>
        </view>
      </view>

      <!-- 生命呼吸核心 -->
      <view class="life-core">
        <view class="core-inner"></view>
        <view class="core-middle"></view>
        <view class="core-outer"></view>
      </view>
    </view>
  </view>
</view>
