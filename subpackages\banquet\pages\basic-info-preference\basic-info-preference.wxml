<view class="container">
  <!-- 基本信息 -->
  <van-cell-group custom-class="info-group">
    <van-cell title="年龄" value="{{userInfo.age || '27'}}" is-link bind:click="editAge" custom-class="info-cell" />
    <van-cell title="性别" value="{{userInfo.gender || '男'}}" is-link bind:click="editGender" custom-class="info-cell" />
    <van-cell title="身高" value="{{userInfo.height || '180'}}" is-link bind:click="editHeight"
      custom-class="info-cell" />
    <van-cell title="体重" value="{{userInfo.weight || '140'}}" is-link bind:click="editWeight"
      custom-class="info-cell" />
  </van-cell-group>

  <!-- 偏好信息 -->
  <van-cell-group custom-class="preference-group">
    <van-cell title="日常活动量" value="{{userInfo.activityLevel || '很少活动'}}" is-link bind:click="editActivityLevel"
      custom-class="info-cell" />
    <van-cell title="口味偏好" value="{{userInfo.tastePreference || '清淡'}}" is-link bind:click="editTastePreference"
      custom-class="info-cell" />
    <van-cell title="饮食目标" value="{{userInfo.dietGoal || '维持健康'}}" is-link bind:click="editDietGoal"
      custom-class="info-cell" />
  </van-cell-group>

  <!-- 过敏原 -->
  <view class="tag-section">
    <view class="tag-header">
      <text class="tag-title">过敏原</text>
      <van-icon name="plus" class="add-icon" bind:click="addAllergy" />
    </view>
    
    <view class="tag-container">
      <view wx:for="{{allergyList}}" wx:key="index" class="custom-tag">
        <text class="tag-text">{{item}}</text>
        <van-icon name="cross" size="25rpx" catchtap="removeAllergy" data-index="{{index}}" class="delete-icon" />
      </view>
    </view>
  </view>

  <!-- 食材偏好 -->
  <view class="tag-section">
    <view class="tag-header">
      <text class="tag-title">食材偏好</text>
      <van-icon name="plus" class="add-icon" bind:click="addFoodPreference" />
    </view>
    <view class="tag-container">
      <view wx:for="{{foodPreferenceList}}" wx:key="index" class="custom-tag">
        <text class="tag-text">{{item}}</text>
        <van-icon name="cross" size="25rpx" catchtap="removeFoodPreference" data-index="{{index}}" class="delete-icon" />
      </view>
    </view>
  </view>

  <!-- Picker 弹窗 -->
  <van-popup show="{{showPicker}}" position="bottom" bind:close="onPickerClose" custom-class="picker-popup">
    <view class="picker-container">
      <!-- 标题 -->
      <view class="picker-header">
        <text class="picker-title">{{pickerTitle}}</text>
      </view>

      <!-- Picker 主体 -->
      <van-picker
        columns="{{pickerColumns}}"
        default-index="{{pickerDefaultIndex}}"
        bind:change="onPickerChange"
        custom-class="custom-picker"
      />

      <!-- 自定义按钮 -->
      <view class="picker-buttons">
        <view class="picker-button cancel-button" bindtap="onPickerCancel">
          <text class="button-text">取消</text>
        </view>
        <view class="picker-button confirm-button" bindtap="onPickerConfirm">
          <text class="button-text">确定</text>
        </view>
      </view>
    </view>
  </van-popup>
</view>