/**
 * 功能导航区域组件
 * 包含椭圆环形菜单、星系旋转效果、近大远小视觉效果等
 * 使用ES6语法编写
 */

// 配置常量
const CONFIG = {
  // 动画配置
  ANIMATION: {
    HIGHLIGHT_TIMEOUT: 300,  // 高亮持续时间(ms)
    RIPPLE_TIMEOUT: 400      // 涟漪效果持续时间(ms)
  },
  // 椭圆配置
  ELLIPSE: {
    centerX: 50,    // 椭圆中心X坐标(%)
    centerY: 50,    // 椭圆中心Y坐标(%)
    radiusX: 35,    // 椭圆X轴半径(%)
    radiusY: 20     // 椭圆Y轴半径(%)
  },
  // 滚动缩放配置
  SCROLL_SCALE: {
    MAX_SCALE: 1.5,        // 最大缩放比例
    MIN_SCALE: 0.6,        // 最小缩放比例
    INFLUENCE_RANGE: 1.3,  // 影响范围
    MAX_OPACITY: 1.0,      // 最大透明度
    MIN_OPACITY: 0.3,      // 最小透明度
    MAX_BLUR: 0,           // 最大清晰度
    MIN_BLUR: 0.4          // 最小清晰度(模糊度)
  },
  // 惯性滑动配置
  INERTIA: {
    FRICTION: 0.99,           // 摩擦系数
    MIN_VELOCITY: 0.1,        // 最小速度阈值
    VELOCITY_SCALE: 1.2,      // 速度缩放因子
    MAX_VELOCITY: 40,         // 最大速度限制
    FRAME_RATE: 60,           // 动画帧率
    BOOST_FACTOR: 1.5         // 惯性增强因子
  }
};

// 默认菜单位置配置
const DEFAULT_MENU_POSITIONS = [
  { index: 0, angle: 180, name: 'family', icon: '/image/home/<USER>', wuxingType: 'family' },
  { index: 1, angle: 240, name: 'group', icon: '/image/home/<USER>', wuxingType: 'fire' },
  { index: 2, angle: 300, name: 'banquet', icon: '/image/home/<USER>', wuxingType: 'earth' },
  { index: 3, angle: 0, name: 'travel', icon: '/image/home/<USER>', wuxingType: 'metal' },
  { index: 4, angle: 60, name: 'punk', icon: '/image/home/<USER>', wuxingType: 'water' },
  { index: 5, angle: 120, name: 'chronic', icon: '/image/home/<USER>', wuxingType: 'wood' }
];

/**
 * 功能导航组件类
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 菜单项配置数组
     * @type {Array}
     * @default DEFAULT_MENU_POSITIONS
     */
    menuItems: {
      type: Array,
      value: DEFAULT_MENU_POSITIONS
    },

    /**
     * 是否启用动画效果
     * @type {Boolean}
     * @default true
     */
    enableAnimation: {
      type: Boolean,
      value: true
    },

    /**
     * 是否启用触摸交互
     * @type {Boolean}
     * @default true
     */
    enableTouch: {
      type: Boolean,
      value: true
    },

    /**
     * 滚动位置(用于近大远小效果)
     * @type {Number}
     * @default 0
     */
    scrollTop: {
      type: Number,
      value: 0
    },

    /**
     * 功能导航区域的位置信息
     * @type {Object}
     * @default null
     */
    functionNavRect: {
      type: Object,
      value: null
    },

    /**
     * 屏幕中心Y坐标
     * @type {Number}
     * @default 0
     */
    screenCenterY: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 图片路径配置
    bgImagePath: '/image/home/<USER>',
    robotImagePath: '/image/home/<USER>',
    taijiImagePath: '/image/home/<USER>',

    // 椭圆配置
    ellipseConfig: CONFIG.ELLIPSE,
    
    // 计算后的菜单位置
    calculatedMenuPositions: [],
    
    // 当前选中的菜单项索引
    currentCircleIndex: -1,
    
    // 涟漪效果索引
    selectionRippleIndex: -1,
    
    // 是否正在旋转
    isCircleRotating: false,
    
    // 是否正在执行菜单动画
    isMenuAnimating: false,
    
    // 强制重置菜单状态标志
    forceResetMenu: false,

    // 动画控制
    energyFlowActive: false,      // 能量流转动画
    constellationActive: true,    // 星宿轨道动画
    lifeCoreBreathing: true,      // 生命核心呼吸动画

    // 星系旋转系统状态
    galaxyRotation: {
      angle: 0,                   // 当前旋转角度
      isUserDragging: false,      // 用户是否正在拖拽
      velocity: 0,                // 当前旋转速度
      lastAngle: 0,               // 上一次的角度
      lastTimestamp: 0,           // 上一次的时间戳
      isInertiaActive: false,     // 惯性滑动是否激活
      velocityHistory: [],        // 速度历史记录
      initialAngle: 0,            // 触摸开始时的角度
      touchCenterX: 0,            // 触摸中心点X
      touchCenterY: 0             // 触摸中心点Y
    },

    // 触摸相关
    touchStartAngle: 0,           // 触摸开始角度

    // 设备性能检测
    supportBlurEffect: true       // 是否支持模糊效果
  },

  /**
   * 组件生命周期 - 组件实例刚刚被创建
   */
  created() {
    // 初始化定时器引用
    this.highlightTimer = null;
    this.scrollTimer = null;
    this.inertiaAnimationId = null;
  },

  /**
   * 组件生命周期 - 组件实例进入页面节点树
   */
  attached() {
    this.initComponent();
  },

  /**
   * 组件生命周期 - 组件实例被从页面节点树移除
   */
  detached() {
    this.clearAllTimers();
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     * @description 设置初始状态，计算菜单位置，启动动画
     */
    initComponent() {
      this.detectDevicePerformance();
      this.initCircleMenuItems();
      this.initTraditionalAnimations();
      this.calculateMenuPositions();
    },

    /**
     * 检测设备性能
     * @description 根据设备信息判断是否支持高级视觉效果
     */
    detectDevicePerformance() {
      try {
        const systemInfo = wx.getSystemInfoSync();
        const supportBlurEffect = this.checkBlurSupport(systemInfo);
        this.setData({ supportBlurEffect });
      } catch (error) {
        console.warn('设备性能检测失败:', error);
        this.setData({ supportBlurEffect: false });
      }
    },

    /**
     * 检测设备是否支持模糊效果
     * @param {Object} systemInfo - 系统信息
     * @returns {Boolean} 是否支持模糊效果
     */
    checkBlurSupport(systemInfo) {
      const { platform, model, version } = systemInfo;
      
      // iOS设备通常支持较好的CSS滤镜效果
      if (platform === 'ios') {
        return true;
      }
      
      // Android设备根据版本和型号判断
      if (platform === 'android') {
        const androidVersion = parseFloat(version);
        if (androidVersion < 7.0) {
          return false;
        }
        
        // 检查是否为低端设备
        const lowEndKeywords = ['lite', 'go', 'mini', 'youth', 'se'];
        const modelLower = (model || '').toLowerCase();
        const isLowEnd = lowEndKeywords.some(keyword => modelLower.includes(keyword));
        
        return !isLowEnd;
      }
      
      return false;
    },

    /**
     * 初始化环形菜单项
     * @description 设置初始状态和位置
     */
    initCircleMenuItems() {
      this.setData({
        currentCircleIndex: -1,
        calculatedMenuPositions: []
      });
    },

    /**
     * 初始化传统动画效果
     * @description 启动各种背景动画效果
     */
    initTraditionalAnimations() {
      if (!this.data.enableAnimation) return;

      this.setData({
        energyFlowActive: true,
        constellationActive: true,
        lifeCoreBreathing: true
      });
    },

    /**
     * 计算菜单项位置
     * @description 根据椭圆轨道、旋转角度、滚动位置等计算每个菜单项的最终位置和样式
     */
    calculateMenuPositions() {
      const {
        ellipseConfig,
        galaxyRotation,
        scrollTop,
        functionNavRect,
        screenCenterY,
        supportBlurEffect
      } = this.data;

      const menuItems = this.properties.menuItems || DEFAULT_MENU_POSITIONS;

      const positions = menuItems.map(item => {
        // 计算总角度（基础角度 + 旋转角度）
        const totalAngle = item.angle + galaxyRotation.angle;
        const angleRad = (totalAngle * Math.PI) / 180;
        const cosAngle = Math.cos(angleRad);
        const sinAngle = Math.sin(angleRad);

        // 计算椭圆轨道上的基础位置
        const baseX = ellipseConfig.centerX + ellipseConfig.radiusX * cosAngle;
        const baseY = ellipseConfig.centerY + ellipseConfig.radiusY * sinAngle;

        // 初始化视觉效果参数
        let scale = 1;
        let opacity = 1;
        let blur = 0;

        // 如果有滚动信息，计算近大远小效果
        if (functionNavRect && screenCenterY > 0) {
          const result = this.calculateScrollEffects(baseY, functionNavRect, screenCenterY, scrollTop, supportBlurEffect);
          scale = result.scale;
          opacity = result.opacity;
          blur = result.blur;
        }

        return {
          ...item,
          x: baseX,
          y: baseY,
          currentAngle: totalAngle,
          scale,
          opacity,
          blur
        };
      });

      this.setData({ calculatedMenuPositions: positions });
    },

    /**
     * 计算滚动相关的视觉效果
     * @param {Number} baseY - 基础Y坐标
     * @param {Object} functionNavRect - 功能导航区域位置信息
     * @param {Number} screenCenterY - 屏幕中心Y坐标
     * @param {Number} scrollTop - 滚动位置
     * @param {Boolean} supportBlurEffect - 是否支持模糊效果
     * @returns {Object} 包含scale、opacity、blur的对象
     */
    calculateScrollEffects(baseY, functionNavRect, screenCenterY, scrollTop, supportBlurEffect) {
      // 计算图标在屏幕中的实际Y位置
      const iconAbsoluteY = functionNavRect.top + (baseY / 100) * functionNavRect.height;
      const iconScreenY = iconAbsoluteY - scrollTop;

      // 计算距离屏幕中心的距离
      const distanceFromCenter = Math.abs(iconScreenY - screenCenterY);
      const maxDistance = screenCenterY * CONFIG.SCROLL_SCALE.INFLUENCE_RANGE;
      const normalizedDistance = Math.min(distanceFromCenter / maxDistance, 1);

      // 计算缩放比例（距离中心越近越大）
      const scaleRange = CONFIG.SCROLL_SCALE.MAX_SCALE - CONFIG.SCROLL_SCALE.MIN_SCALE;
      let scale = CONFIG.SCROLL_SCALE.MAX_SCALE - (Math.pow(normalizedDistance, 0.7) * scaleRange);

      // 计算透明度（距离中心越近越清晰）
      const opacityRange = CONFIG.SCROLL_SCALE.MAX_OPACITY - CONFIG.SCROLL_SCALE.MIN_OPACITY;
      let opacity = CONFIG.SCROLL_SCALE.MAX_OPACITY - (Math.pow(normalizedDistance, 0.6) * opacityRange);

      // 计算模糊度（根据设备支持情况）
      let blur = 0;
      if (supportBlurEffect) {
        const blurRange = CONFIG.SCROLL_SCALE.MIN_BLUR - CONFIG.SCROLL_SCALE.MAX_BLUR;
        blur = CONFIG.SCROLL_SCALE.MAX_BLUR + (Math.pow(normalizedDistance, 0.7) * blurRange);
      } else {
        // 不支持模糊效果的设备，使用标记值
        blur = normalizedDistance > 0.6 ? 1 : 0;
      }

      // 确保值在合理范围内
      scale = Math.max(CONFIG.SCROLL_SCALE.MIN_SCALE, Math.min(CONFIG.SCROLL_SCALE.MAX_SCALE, scale));
      opacity = Math.max(CONFIG.SCROLL_SCALE.MIN_OPACITY, Math.min(CONFIG.SCROLL_SCALE.MAX_OPACITY, opacity));
      if (supportBlurEffect) {
        blur = Math.max(CONFIG.SCROLL_SCALE.MAX_BLUR, Math.min(CONFIG.SCROLL_SCALE.MIN_BLUR, blur));
      }

      return { scale, opacity, blur };
    },

    /**
     * 处理菜单项点击事件
     * @param {Object} e - 事件对象
     * @description 处理菜单项的点击，包括高亮效果、涟漪动画和事件触发
     */
    handleCircleItemClick(e) {
      const { index, type } = e.currentTarget.dataset;
      const numericIndex = parseInt(index, 10);

      this.clearHighlightTimer();

      // 设置短暂的激活状态用于视觉反馈
      this.setData({
        currentCircleIndex: numericIndex,
        selectionRippleIndex: numericIndex
      });

      // 触发震动反馈
      wx.vibrateShort({ type: 'light' });

      // 涟漪效果结束后清除
      setTimeout(() => {
        this.setData({ selectionRippleIndex: -1 });
      }, CONFIG.ANIMATION.RIPPLE_TIMEOUT);

      // 快速清除高亮状态，避免按钮长时间保持发光
      this.highlightTimer = setTimeout(() => {
        this.setData({ currentCircleIndex: -1 });
      }, CONFIG.ANIMATION.HIGHLIGHT_TIMEOUT);

      // 触发自定义事件，通知父组件
      this.triggerEvent('menuItemClick', {
        index: numericIndex,
        type: type,
        item: this.data.calculatedMenuPositions[numericIndex]
      });
    },

    /**
     * 处理触摸开始事件
     * @param {Object} e - 触摸事件对象
     * @description 初始化触摸状态，开始旋转交互
     */
    handleTouchStart(e) {
      if (!this.properties.enableTouch || e.touches.length !== 1) return;

      const { clientX, clientY } = e.touches[0];

      // 动态计算触摸中心点
      this.getTouchCenter((centerX, centerY) => {
        const startAngle = Math.atan2(clientY - centerY, clientX - centerX) * 180 / Math.PI;
        const currentTime = Date.now();

        // 平滑停止惯性动画
        if (this.data.galaxyRotation.isInertiaActive) {
          this.stopInertiaAnimation();
          setTimeout(() => {
            this.initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY);
          }, 16);
        } else {
          this.initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY);
        }
      });
    },

    /**
     * 初始化触摸状态
     * @param {Number} clientX - 触摸点X坐标
     * @param {Number} clientY - 触摸点Y坐标
     * @param {Number} startAngle - 开始角度
     * @param {Number} currentTime - 当前时间戳
     * @param {Number} centerX - 中心点X坐标
     * @param {Number} centerY - 中心点Y坐标
     */
    initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY) {
      this.setData({
        touchStartAngle: startAngle,
        'galaxyRotation.isUserDragging': true,
        'galaxyRotation.lastAngle': this.data.galaxyRotation.angle,
        'galaxyRotation.initialAngle': this.data.galaxyRotation.angle,
        'galaxyRotation.lastTimestamp': currentTime,
        'galaxyRotation.velocity': 0,
        'galaxyRotation.isInertiaActive': false,
        'galaxyRotation.velocityHistory': [],
        'galaxyRotation.touchCenterX': centerX,
        'galaxyRotation.touchCenterY': centerY,
        isCircleRotating: true
      });

      // 清理滚动定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    /**
     * 处理触摸移动事件
     * @param {Object} e - 触摸事件对象
     * @description 计算旋转角度，更新菜单位置
     */
    handleTouchMove(e) {
      const {
        isCircleRotating,
        galaxyRotation: { isUserDragging, lastTimestamp, touchCenterX, touchCenterY }
      } = this.data;

      if (!this.properties.enableTouch || !isCircleRotating || e.touches.length !== 1 || !isUserDragging) return;

      const { clientX, clientY } = e.touches[0];
      const currentTime = Date.now();

      // 计算当前角度
      const currentAngle = Math.atan2(clientY - touchCenterY, clientX - touchCenterX) * 180 / Math.PI;
      const angleDiff = this.calculateAngleDifference(currentAngle, this.data.touchStartAngle);
      const newAngle = this.data.galaxyRotation.initialAngle + angleDiff;

      // 计算速度
      const timeDiff = Math.max(currentTime - lastTimestamp, 1);
      const velocity = angleDiff / timeDiff;

      // 更新速度历史
      const velocityHistory = [...this.data.galaxyRotation.velocityHistory, velocity].slice(-5);

      this.setData({
        'galaxyRotation.angle': newAngle,
        'galaxyRotation.velocity': velocity,
        'galaxyRotation.lastTimestamp': currentTime,
        'galaxyRotation.velocityHistory': velocityHistory
      });

      // 直接计算位置
      this.calculateMenuPositions();
    },

    /**
     * 处理触摸结束事件
     * @description 结束触摸交互，启动惯性滑动
     */
    handleTouchEnd() {
      if (!this.properties.enableTouch || !this.data.isCircleRotating) return;

      const { galaxyRotation: { velocity, velocityHistory } } = this.data;

      this.setData({
        'galaxyRotation.isUserDragging': false,
        isCircleRotating: false
      });

      // 计算平均速度用于惯性滑动
      if (velocityHistory.length > 0) {
        const avgVelocity = velocityHistory.reduce((sum, v) => sum + v, 0) / velocityHistory.length;
        this.startInertiaAnimation(avgVelocity);
      }
    },

    /**
     * 开始惯性滑动动画
     * @param {Number} initialVelocity - 初始速度
     */
    startInertiaAnimation(initialVelocity) {
      // 增强惯性效果
      let velocity = initialVelocity * CONFIG.INERTIA.BOOST_FACTOR * CONFIG.INERTIA.VELOCITY_SCALE;
      velocity = Math.max(-CONFIG.INERTIA.MAX_VELOCITY, Math.min(CONFIG.INERTIA.MAX_VELOCITY, velocity));

      this.setData({
        'galaxyRotation.velocity': velocity,
        'galaxyRotation.isInertiaActive': true
      });

      this.runInertiaAnimation();
    },

    /**
     * 运行惯性滑动动画
     * @description 持续更新旋转角度直到速度衰减到最小值
     */
    runInertiaAnimation() {
      if (!this.data.galaxyRotation.isInertiaActive) return;

      const { galaxyRotation: { velocity, angle } } = this.data;

      // 检查速度是否足够小
      if (Math.abs(velocity) < CONFIG.INERTIA.MIN_VELOCITY) {
        this.stopInertiaAnimation();
        return;
      }

      // 更新角度和速度
      const newAngle = angle + velocity;
      const newVelocity = velocity * CONFIG.INERTIA.FRICTION;

      this.setData({
        'galaxyRotation.angle': newAngle,
        'galaxyRotation.velocity': newVelocity
      });

      // 更新菜单位置
      this.calculateMenuPositions();

      // 继续动画
      this.inertiaAnimationId = setTimeout(() => {
        this.runInertiaAnimation();
      }, 1000 / CONFIG.INERTIA.FRAME_RATE);
    },

    /**
     * 停止惯性滑动动画
     */
    stopInertiaAnimation() {
      if (this.inertiaAnimationId) {
        clearTimeout(this.inertiaAnimationId);
        this.inertiaAnimationId = null;
      }

      this.setData({
        'galaxyRotation.isInertiaActive': false,
        'galaxyRotation.velocity': 0
      });
    },

    /**
     * 计算角度差值
     * @param {Number} angle1 - 角度1
     * @param {Number} angle2 - 角度2
     * @returns {Number} 角度差值
     */
    calculateAngleDifference(angle1, angle2) {
      let diff = angle1 - angle2;
      while (diff > 180) diff -= 360;
      while (diff < -180) diff += 360;
      return diff;
    },

    /**
     * 获取触摸中心点
     * @param {Function} callback - 回调函数，接收(centerX, centerY)参数
     */
    getTouchCenter(callback) {
      const query = this.createSelectorQuery();
      query.select('.elliptical-menu').boundingClientRect((rect) => {
        if (rect) {
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          callback(centerX, centerY);
        }
      }).exec();
    },

    /**
     * 清除高亮定时器
     */
    clearHighlightTimer() {
      if (this.highlightTimer) {
        clearTimeout(this.highlightTimer);
        this.highlightTimer = null;
      }
    },

    /**
     * 清除所有定时器
     * @description 清理组件中的所有定时器，防止内存泄漏
     */
    clearAllTimers() {
      this.clearHighlightTimer();

      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = null;
      }

      this.stopInertiaAnimation();
    },

    /**
     * 重置菜单状态
     * @description 重置所有菜单相关的状态到初始值
     */
    resetMenuStates() {
      this.clearAllTimers();
      this.setData({
        currentCircleIndex: -1,
        selectionRippleIndex: -1,
        forceResetMenu: false,
        isCircleRotating: false,
        'galaxyRotation.isUserDragging': false,
        'galaxyRotation.isInertiaActive': false,
        'galaxyRotation.velocity': 0
      });
    },

    /**
     * 强制重置菜单状态（用于手机端兼容）
     * @description 使用强制重置标志来确保状态被正确清除
     */
    forceResetMenuStates() {
      this.clearAllTimers();

      this.setData({
        forceResetMenu: true,
        currentCircleIndex: -1,
        selectionRippleIndex: -1
      });

      setTimeout(() => {
        this.setData({ forceResetMenu: false });
      }, 100);
    },

    /**
     * 更新滚动位置
     * @param {Number} scrollTop - 新的滚动位置
     * @description 外部调用此方法来更新滚动位置，触发近大远小效果重新计算
     */
    updateScrollPosition(scrollTop) {
      this.setData({ scrollTop });
      this.calculateMenuPositions();
    },

    /**
     * 更新功能导航区域位置信息
     * @param {Object} rect - 位置信息对象
     * @description 外部调用此方法来更新功能导航区域的位置信息
     */
    updateFunctionNavRect(rect) {
      this.setData({ functionNavRect: rect });
      this.calculateMenuPositions();
    },

    /**
     * 获取当前菜单状态
     * @returns {Object} 当前菜单的状态信息
     */
    getMenuState() {
      return {
        currentCircleIndex: this.data.currentCircleIndex,
        galaxyRotation: this.data.galaxyRotation,
        calculatedMenuPositions: this.data.calculatedMenuPositions,
        isCircleRotating: this.data.isCircleRotating
      };
    },

    /**
     * 设置菜单旋转角度
     * @param {Number} angle - 目标角度
     * @param {Boolean} animated - 是否使用动画
     */
    setRotationAngle(angle, animated = true) {
      if (animated) {
        this.setData({ isMenuAnimating: true });
        setTimeout(() => {
          this.setData({ isMenuAnimating: false });
        }, 600);
      }

      this.setData({ 'galaxyRotation.angle': angle });
      this.calculateMenuPositions();
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听菜单项变化
     * @param {Array} newMenuItems - 新的菜单项数组
     */
    'menuItems': function(newMenuItems) {
      if (newMenuItems && newMenuItems.length > 0) {
        this.calculateMenuPositions();
      }
    },

    /**
     * 监听滚动位置变化
     * @param {Number} newScrollTop - 新的滚动位置
     */
    'scrollTop': function(newScrollTop) {
      if (typeof newScrollTop === 'number') {
        this.calculateMenuPositions();
      }
    },

    /**
     * 监听功能导航区域位置变化
     * @param {Object} newRect - 新的位置信息
     */
    'functionNavRect': function(newRect) {
      if (newRect) {
        this.calculateMenuPositions();
      }
    }
  }
});
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听菜单项变化
     * @param {Array} newMenuItems - 新的菜单项数组
     */
    'menuItems': function(newMenuItems) {
      if (newMenuItems && newMenuItems.length > 0) {
        this.calculateMenuPositions();
      }
    },

    /**
     * 监听滚动位置变化
     * @param {Number} newScrollTop - 新的滚动位置
     */
    'scrollTop': function(newScrollTop) {
      if (typeof newScrollTop === 'number') {
        this.calculateMenuPositions();
      }
    }
  }
});
