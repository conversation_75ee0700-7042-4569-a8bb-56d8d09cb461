Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#3cc51f",
    showTabBarPages: [
      "/pages/index/index",
      "/pages/ai-chat/ai-chat",
      "/pages/user/user",
    ],
    show: true, // 控制tabbar显示隐藏
    list: [{
      pagePath: "/pages/index/index",
      iconPath: "/image/home/<USER>",
      selectedIconPath: "/image/home/<USER>",
      text: "首页"
    }, {
      pagePath: "/pages/ai-chat/ai-chat",
      iconPath: "/image/home/<USER>",
      selectedIconPath: "/image/home/<USER>",
      text: "餐小参"
    }, {
      pagePath: "/subpackages/main/pages/family-nutrition/family-nutrition",
      iconPath: "/image/home/<USER>",
      selectedIconPath: "/image/home/<USER>",
      text: "健康餐圈"
    }, {
      pagePath: "/pages/user/user",
      iconPath: "/image/home/<USER>",
      selectedIconPath: "/image/home/<USER>",
      text: "我的"
    }]
  },
  attached() {
    // 初始化时可能还没有页面栈，延迟执行
    setTimeout(() => {
      this.setData({
        selected: this.getTabIndex()
      });
      this.checkTabBarVisibility();
    }, 100);
  },

  pageLifetimes: {
    show() {
      this.setData({
        selected: this.getTabIndex()
      });
      // 检查当前页面是否应该显示tabbar
      this.checkTabBarVisibility();
    }
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      console.log(url,'url')
      wx.switchTab({url})
      this.setData({
        selected: data.index
      })
    },

    getTabIndex() {
      const pages = getCurrentPages();
      // 检查页面栈是否为空
      if (!pages || pages.length === 0) {
        return 0;
      }
      
      const currentPage = pages[pages.length - 1];
      // 检查currentPage是否存在
      if (!currentPage || !currentPage.route) {
        return 0;
      }
      
      const currentPath = '/' + currentPage.route;
      
      for (let i = 0; i < this.data.list.length; i++) {
        if (this.data.list[i].pagePath === currentPath) {
          return i;
        }
      }
      return 0;
    },

    checkTabBarVisibility() {
      const pages = getCurrentPages();
      // 检查页面栈是否为空
      if (!pages || pages.length === 0) {
        // 默认显示tabbar
        this.setData({
          show: true
        });
        return;
      }
      
      const currentPage = pages[pages.length - 1];
      // 检查currentPage是否存在
      if (!currentPage || !currentPage.route) {
        // 默认显示tabbar
        this.setData({
          show: true
        });
        return;
      }
      
      const currentPath = '/' + currentPage.route;
      
      // 检查当前页面是否在需要显示tabbar的页面列表中
      const shouldShow = this.data.showTabBarPages.includes(currentPath);
      
      // 设置tabbar的显示或隐藏
      this.setData({
        show: shouldShow
      });
    }
  }
})