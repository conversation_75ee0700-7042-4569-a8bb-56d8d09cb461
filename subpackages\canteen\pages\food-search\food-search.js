import { searchFoods } from '../../api/food'

Page({
  data: {
    searchValue: '',
    searchHistory: [], // 搜索历史
    searchResults: [],
    loading: false,
    page: 1,
    limit: 20,
    hasMore: true,
    hasSearched: false, // 是否已经搜索过
    searchType: 'food', // 默认搜索类型为食材
    source: '', // 来源页面标识
    type:'' //terry new add
  },

  onLoad(options) {
    // 从本地缓存加载搜索历史
    this.loadSearchHistory();

    // 验证 terry begin
    if (options.type && ['favorite', 'forbidden'].includes(options.type)) {
      this.setData({ targetType: options.type });
    } else {
      console.warn('无效的type参数，使用默认值');
    }
    this.setData({
      type: options.type,
      dayIndex: options.dayIndex
    });
    // 验证 terry end
    
    // 获取传入的搜索类型参数
    if (options.search_type) {
      this.setData({
        searchType: options.search_type
      });
    }
    
    // 获取来源页面标识
    if (options.source) {
      this.setData({
        source: options.source
      });
    }
    
    // 根据搜索类型设置页面标题
    const title = this.data.searchType === 'food' ? '食材搜索' : '菜品搜索';
    wx.setNavigationBarTitle({
      title: title
    });
  },

  // 从本地缓存加载搜索历史
  loadSearchHistory() {
    try {
      const history = wx.getStorageSync('food_search_history');
      if (history && Array.isArray(history)) {
        this.setData({
          searchHistory: history
        });
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error);
    }
  },

  // 保存搜索历史到本地缓存
  saveSearchHistory(history) {
    try {
      wx.setStorageSync('food_search_history', history);
    } catch (error) {
      console.error('保存搜索历史失败:', error);
    }
  },

  // 搜索输入变化
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  // 搜索确认
  onSearchConfirm() {
    if (this.data.searchValue.trim()) {
      this.performSearch(this.data.searchValue.trim());
    }
  },

  // 执行搜索
  async performSearch(keyword, page = 1) {
    if (this.data.loading) return;
    
    this.setData({
      loading: true,
      hasSearched: true
    });

    try {
      const params = {
        keyword,
        page,
        limit: this.data.limit,
        search_type: this.data.searchType // 添加搜索类型参数
      };

      const response = await searchFoods(params);
      
      if (response && response.data) {
        const results = response.data.map(item => ({
          id: item.id,
          name: item.name,
          type: item.class_name || (this.data.searchType === 'food' ? '食材' : '菜品'),
          emoji: item.icon || (this.data.searchType === 'food' ? '🍎' : '🍲'), // 根据类型使用不同的默认emoji
          constitution_name: item.constitution_name,
          fat: item.fat,
          protein: item.protein,
          carbohydrates: item.carbohydrates,
          energy: item.energy,
          fiber: item.fiber,
          class_name: item.class_name,
          icon: item.icon,
          tag: item.tag,
          // 保存原始数据，用于传递给上一个页面
          raw: item
        }));

        if (page === 1) {
          // 新搜索，替换结果
          this.setData({
            searchResults: results,
            page: 1,
            hasMore: results.length === this.data.limit
          });
          console.log('搜索到的数据Terry：',this.data.searchResults)
        } else {
          // 加载更多，追加结果
          this.setData({
            searchResults: [...this.data.searchResults, ...results],
            hasMore: results.length === this.data.limit
          });
        }

        // 将搜索关键词添加到历史记录
        this.addToSearchHistory(keyword);
      }
    } catch (error) {
      console.error('搜索失败:', error);
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false
      });
      
      // 停止下拉刷新
      wx.stopPullDownRefresh();
    }
  },

  // 添加到搜索历史
  addToSearchHistory(keyword) {
    let history = [...this.data.searchHistory];
    
    // 移除重复项
    history = history.filter(item => item !== keyword);
    
    // 添加到开头
    history.unshift(keyword);
    
    // 限制历史记录数量
    if (history.length > 10) {
      history = history.slice(0, 10);
    }
    
    this.setData({
      searchHistory: history
    });
    
    // 保存到本地缓存
    this.saveSearchHistory(history);
  },

  // 清空搜索
  onClearSearch() {
    this.setData({
      searchValue: '',
      searchResults: [],
      hasSearched: false
    });
  },

  // 点击搜索历史
  onHistoryClick(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchValue: keyword
    });
    this.performSearch(keyword);
  },

  // 清空搜索历史
  onClearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            searchHistory: []
          });
          // 清空本地缓存
          this.saveSearchHistory([]);
        }
      }
    });
  },

  // 点击搜索结果
  onResultClick(e) {
    const item = e.currentTarget.dataset.item;
    console.log('选择' + (this.data.searchType === 'food' ? '食材' : '菜品') + ':', item);

    // terry begin
    const foodItem = {
      id: e.currentTarget.dataset.item.id || Date.now(), // 确保有id
      name: e.currentTarget.dataset.item.name
    };
    const index = e.currentTarget.dataset.index;
    const selectedMeal = this.data.searchResults[index];

    // 转换参数名称
    const productToAdd = {
      selectedMeal: selectedMeal,
      type: this.data.type,
      dayIndex: this.data.dayIndex,
      dish_icon: selectedMeal.icon,
      dish_name: selectedMeal.name
    };
    console.log('转换参数名称:',productToAdd)
    // terry end
    
    // 将选择的食材/菜品和类型传递给上一个页面
    const pages = getCurrentPages();
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2];

      // 确保传递type参数 terry begin
      if (prevPage.addFoodToBox) {
        prevPage.addFoodToBox(foodItem, this.data.targetType);
      }
      if (prevPage?.addSelectedMeal) {
        prevPage.addSelectedMeal(productToAdd);
      } else {
        // console.error('未找到首页或addSelectedMeal方法');
      }
      // 确保传递type参数 terry end

      // 根据来源调用不同的回调方法
      if (this.data.source === 'cost-settings') {
        if (prevPage.onFoodSelectedFromSearch) {
          // 直接传递整个API返回的数据
          prevPage.onFoodSelectedFromSearch({
            data: [item.raw || item]
          });
        }
      } else if (this.data.source === 'allergy' || this.data.source === 'preference') {
        // 来自基本信息及偏好页面的调用
        if (prevPage.onFoodSelected) {
          prevPage.onFoodSelected({
            item: item,
            type: this.data.searchType,
            source: this.data.source
          });
        }
      } else if (prevPage.onFoodSelected) {
        // 默认的回调方法
        prevPage.onFoodSelected({
          item: item,
          type: this.data.searchType
        });
      }
    }
    
    wx.navigateBack({
      delta: 1
    });
  },

  // 返回按钮点击
  onGoBack() {
    wx.navigateBack();
  },

  // 录像按钮点击
  onRecordClick() {
    // 录像功能逻辑
    console.log('录像功能');
  },

  // 页面触底加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading && this.data.searchValue.trim()) {
      this.setData({
        page: this.data.page + 1
      });
      this.performSearch(this.data.searchValue.trim(), this.data.page);
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    if (this.data.searchValue.trim()) {
      this.performSearch(this.data.searchValue.trim());
    } else {
      wx.stopPullDownRefresh();
    }
  }
}); 