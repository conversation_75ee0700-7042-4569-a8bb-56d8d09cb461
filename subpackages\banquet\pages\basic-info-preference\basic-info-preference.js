Page({
  data: {
    userInfo: {
      age: '27',
      gender: '男',
      height: '180',
      weight: '140',
      activityLevel: '很少活动',
      tastePreference: '清淡',
      dietGoal: '维持健康'
    },
    allergyList: ['猪肉', '猪肉', '猪肉'],
    foodPreferenceList: ['猪肉', '猪肉', '猪肉'],

    // Picker 相关数据
    showPicker: false,
    pickerTitle: '',
    pickerColumns: [],
    currentPickerType: '', // 当前选择器类型
    selectedPickerValue: '', // 当前选中的值

    // 枚举选项
    genderOptions: ['男', '女'],
    activityLevelOptions: ['很少活动', '轻度活动', '中度活动', '重度活动'],
    tastePreferenceOptions: ['清淡', '适中', '重口味'],
    dietGoalOptions: ['减重', '维持健康', '增重', '增肌'],

    // 年龄范围 (18-100)
    ageOptions: Array.from({length: 83}, (_, i) => (i + 3).toString()),
    // 身高范围 (120-220cm)
    heightOptions: Array.from({length: 101}, (_, i) => (i + 120).toString()),
    // 体重范围 (30-200kg)
    weightOptions: Array.from({length: 171}, (_, i) => (i + 30).toString())
  },

  onLoad() {
    this.loadUserInfo();
  },

  // 加载用户信息
  loadUserInfo() {
    // 这里可以从本地存储或服务器获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || this.data.userInfo;
    const allergyList = wx.getStorageSync('allergyList') || this.data.allergyList;
    const foodPreferenceList = wx.getStorageSync('foodPreferenceList') || this.data.foodPreferenceList;
    
    this.setData({
      userInfo,
      allergyList,
      foodPreferenceList
    });
  },

  // 保存用户信息
  saveUserInfo() {
    wx.setStorageSync('userInfo', this.data.userInfo);
    wx.setStorageSync('allergyList', this.data.allergyList);
    wx.setStorageSync('foodPreferenceList', this.data.foodPreferenceList);
  },

  // 显示选择器
  showPickerModal(type, title, options, currentValue) {
    const currentIndex = options.indexOf(currentValue);
    this.setData({
      showPicker: true,
      pickerTitle: title,
      pickerColumns: options,
      currentPickerType: type,
      pickerDefaultIndex: currentIndex >= 0 ? currentIndex : 0,
      selectedPickerValue: currentValue // 初始化选中值
    });
  },

  // 编辑年龄
  editAge() {
    this.showPickerModal('age', '选择年龄', this.data.ageOptions, this.data.userInfo.age);
  },

  // 编辑性别
  editGender() {
    this.showPickerModal('gender', '选择性别', this.data.genderOptions, this.data.userInfo.gender);
  },

  // 编辑身高
  editHeight() {
    this.showPickerModal('height', '选择身高(cm)', this.data.heightOptions, this.data.userInfo.height);
  },

  // 编辑体重
  editWeight() {
    this.showPickerModal('weight', '选择体重(kg)', this.data.weightOptions, this.data.userInfo.weight);
  },

  // 编辑活动量
  editActivityLevel() {
    this.showPickerModal('activityLevel', '选择日常活动量', this.data.activityLevelOptions, this.data.userInfo.activityLevel);
  },

  // 编辑口味偏好
  editTastePreference() {
    this.showPickerModal('tastePreference', '选择口味偏好', this.data.tastePreferenceOptions, this.data.userInfo.tastePreference);
  },

  // 编辑饮食目标
  editDietGoal() {
    this.showPickerModal('dietGoal', '选择饮食目标', this.data.dietGoalOptions, this.data.userInfo.dietGoal);
  },

  // Picker 值变化
  onPickerChange(e) {
    const { value } = e.detail;
    this.setData({
      selectedPickerValue: value
    });
  },

  // Picker 确认选择
  onPickerConfirm() {
    const value = this.data.selectedPickerValue;
    const type = this.data.currentPickerType;

    // 根据类型更新对应的用户信息
    const updateData = {};
    updateData[`userInfo.${type}`] = value;

    this.setData({
      ...updateData,
      showPicker: false
    });

    this.saveUserInfo();

    wx.showToast({
      title: '修改成功',
      icon: 'success'
    });
  },

  // Picker 取消选择
  onPickerCancel() {
    this.setData({
      showPicker: false
    });
  },

  // Picker 关闭
  onPickerClose() {
    this.setData({
      showPicker: false
    });
  },

  // 添加过敏原
  addAllergy() {
    // 跳转到食材搜索页面
    wx.navigateTo({
      url: '/subpackages/canteen/pages/food-search/food-search?search_type=food&source=allergy'
    });
  },

  // 移除过敏原
  removeAllergy(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    if (index >= 0 && index < this.data.allergyList.length) {
      const allergyList = this.data.allergyList.filter((_, i) => i !== index);
      this.setData({
        allergyList
      });
      this.saveUserInfo();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }
  },

  // 添加食材偏好
  addFoodPreference() {
    // 跳转到食材搜索页面
    wx.navigateTo({
      url: '/subpackages/canteen/pages/food-search/food-search?search_type=food&source=preference'
    });
  },

  // 移除食材偏好
  removeFoodPreference(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    if (index >= 0 && index < this.data.foodPreferenceList.length) {
      const foodPreferenceList = this.data.foodPreferenceList.filter((_, i) => i !== index);
      this.setData({
        foodPreferenceList
      });
      this.saveUserInfo();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    }
  },

  // 接收从食材搜索页面返回的数据
  onFoodSelected(data) {
    const { item, source } = data;

    if (source === 'allergy') {
      // 添加到过敏原列表
      const allergyList = [...this.data.allergyList];
      if (!allergyList.includes(item.name)) {
        allergyList.push(item.name);
        this.setData({
          allergyList
        });
        this.saveUserInfo();
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '已存在该过敏原',
          icon: 'none'
        });
      }
    } else if (source === 'preference') {
      // 添加到食材偏好列表
      const foodPreferenceList = [...this.data.foodPreferenceList];
      if (!foodPreferenceList.includes(item.name)) {
        foodPreferenceList.push(item.name);
        this.setData({
          foodPreferenceList
        });
        this.saveUserInfo();
        wx.showToast({
          title: '添加成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '已存在该食材偏好',
          icon: 'none'
        });
      }
    }
  },

  // 页面显示时的处理
  onShow() {
    // 检查是否有从其他页面传递的数据
    const app = getApp();
    if (app.globalData && app.globalData.selectedFood) {
      const { food, source } = app.globalData.selectedFood;

      if (source === 'allergy') {
        const allergyList = [...this.data.allergyList];
        if (!allergyList.includes(food.name)) {
          allergyList.push(food.name);
          this.setData({
            allergyList
          });
          this.saveUserInfo();
        }
      } else if (source === 'preference') {
        const foodPreferenceList = [...this.data.foodPreferenceList];
        if (!foodPreferenceList.includes(food.name)) {
          foodPreferenceList.push(food.name);
          this.setData({
            foodPreferenceList
          });
          this.saveUserInfo();
        }
      }

      // 清除全局数据
      app.globalData.selectedFood = null;
    }
  }
});
