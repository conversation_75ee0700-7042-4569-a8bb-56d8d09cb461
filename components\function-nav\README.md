# 功能导航区域组件 (function-nav)

## 概述

功能导航区域组件是一个高度集成的ES6语法微信小程序组件，包含椭圆环形菜单、星系旋转效果、近大远小视觉效果、触摸交互等功能。组件采用现代化的ES6语法编写，具有良好的可维护性和扩展性。

## 功能特性

- ✨ **椭圆环形菜单** - 动态渲染的椭圆轨道菜单系统
- 🌟 **星系旋转效果** - 支持触摸拖拽和惯性滑动的旋转交互
- 🎯 **近大远小效果** - 基于滚动位置的动态缩放和透明度变化
- 📱 **手机端优化** - 针对不同设备性能的自适应优化
- 🎨 **传统文化元素** - 太极、五行、星宿等中国传统文化视觉效果
- ⚡ **硬件加速** - 使用3D变换和硬件加速确保流畅性能
- 🔧 **ES6语法** - 使用现代JavaScript语法，代码结构清晰

## 组件结构

```
components/function-nav/
├── function-nav.js      # 组件逻辑 (ES6语法)
├── function-nav.wxml    # 组件模板
├── function-nav.wxss    # 组件样式
├── function-nav.json    # 组件配置
└── README.md           # 使用说明
```

## 使用方法

### 1. 注册组件

在页面的 `.json` 文件中注册组件：

```json
{
  "usingComponents": {
    "function-nav": "/components/function-nav/function-nav"
  }
}
```

### 2. 在页面中使用

```xml
<function-nav 
  id="function-nav"
  menu-items="{{menuItems}}"
  enable-animation="{{true}}"
  enable-touch="{{true}}"
  scroll-top="{{scrollTop}}"
  function-nav-rect="{{functionNavRect}}"
  screen-center-y="{{screenCenterY}}"
  bind:menuItemClick="handleMenuItemClick">
</function-nav>
```

### 3. 页面JavaScript配置

```javascript
Page({
  data: {
    // 菜单项配置
    menuItems: [
      { index: 0, angle: 180, name: 'family', icon: '/image/home/<USER>', wuxingType: 'family' },
      { index: 1, angle: 240, name: 'group', icon: '/image/home/<USER>', wuxingType: 'fire' },
      // ... 更多菜单项
    ],
    scrollTop: 0,
    functionNavRect: null,
    screenCenterY: 0
  },

  // 处理菜单项点击事件
  handleMenuItemClick(e) {
    const { index, type, item } = e.detail;
    console.log('菜单项点击:', { index, type, item });
    // 处理点击逻辑
  },

  // 页面滚动事件
  onPageScroll(e) {
    const { scrollTop } = e;
    this.setData({ scrollTop });
    
    // 更新组件滚动位置
    const functionNavComponent = this.selectComponent('function-nav');
    if (functionNavComponent) {
      functionNavComponent.updateScrollPosition(scrollTop);
    }
  },

  // 获取功能导航区域位置
  getFunctionNavRect() {
    const query = wx.createSelectorQuery().in(this);
    query.select('.function-nav').boundingClientRect((rect) => {
      if (rect) {
        this.setData({ 
          functionNavRect: rect,
          screenCenterY: wx.getSystemInfoSync().windowHeight / 2
        });
        
        // 更新组件位置信息
        const functionNavComponent = this.selectComponent('function-nav');
        if (functionNavComponent) {
          functionNavComponent.updateFunctionNavRect(rect);
        }
      }
    }).exec();
  }
});
```

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| menu-items | Array | DEFAULT_MENU_POSITIONS | 菜单项配置数组 |
| enable-animation | Boolean | true | 是否启用动画效果 |
| enable-touch | Boolean | true | 是否启用触摸交互 |
| scroll-top | Number | 0 | 页面滚动位置 |
| function-nav-rect | Object | null | 功能导航区域位置信息 |
| screen-center-y | Number | 0 | 屏幕中心Y坐标 |

## 事件说明

| 事件名 | 说明 | 参数 |
|--------|------|------|
| menuItemClick | 菜单项点击事件 | { index, type, item } |

## 菜单项配置格式

```javascript
{
  index: 0,                           // 菜单项索引
  angle: 180,                         // 在椭圆轨道上的角度
  name: 'family',                     // 菜单项名称/类型
  icon: '/image/home/<USER>',     // 图标路径
  wuxingType: 'family'                // 五行类型，影响光晕颜色
}
```

## 组件方法

组件提供以下公共方法供外部调用：

- `updateScrollPosition(scrollTop)` - 更新滚动位置
- `updateFunctionNavRect(rect)` - 更新功能导航区域位置信息
- `getMenuState()` - 获取当前菜单状态
- `setRotationAngle(angle, animated)` - 设置旋转角度
- `resetMenuStates()` - 重置菜单状态
- `forceResetMenuStates()` - 强制重置菜单状态（手机端兼容）

## 样式定制

组件样式完全封装在 `function-nav.wxss` 中，可以通过修改CSS变量或覆盖样式类来定制外观。

## 性能优化

- 使用硬件加速 (`translateZ(0)`)
- 设备性能检测，自动降级模糊效果
- 惯性滑动优化，流畅的触摸体验
- 动画帧率控制，避免性能问题

## 兼容性

- 支持iOS和Android平台
- 自动检测设备性能，提供最佳体验
- 针对低端设备的降级方案

## 注意事项

1. 确保图片资源路径正确
2. 在页面onLoad后调用getFunctionNavRect()获取位置信息
3. 监听页面滚动事件以实现近大远小效果
4. 组件ID必须设置为便于外部调用方法

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的功能导航区域组件
- ES6语法重构
- 完善的注释和文档
