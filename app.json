{"pages": ["pages/index/index", "pages/user/user", "pages/ai-chat/ai-chat"], "subPackages": [{"root": "subpackages/main", "name": "main", "pages": ["pages/logs/logs", "pages/login/login", "pages/data-entry/data-entry", "pages/user-profile/user-profile", "pages/body-constitution/body-constitution", "pages/allergy-avoidance/allergy-avoidance", "pages/diet-preference/diet-preference", "pages/family-plan/family-plan", "pages/select-name/select-name", "pages/select-member/select-member", "pages/meal-plan/meal-plan", "pages/family-nutrition/family-nutrition", "pages/food/food", "pages/food-search/food-search", "pages/camera/camera", "pages/tongue/tongue", "pages/report-loading/report-loading", "pages/constitution-detail/constitution-detail", "pages/body-report/body-report", "pages/member/index", "pages/member/detail/index"]}, {"root": "subpackages/canteen", "name": "canteen", "pages": ["pages/group-meal/group-meal", "pages/group-progress/group-progress", "pages/group-plan/group-plan", "pages/group-setcan/group-setcan", "pages/simon/simon", "pages/add-record/add-record", "pages/feedback/feedback", "pages/feedback-history/feedback-history", "pages/food-search/food-search", "pages/analysis-range/analysis-range", "pages/feedback-opinions/feedback-opinions", "pages/suggestion-list/suggestion-list", "pages/group-mealbook/group-mealbook", "pages/suggestion-detail/suggestion-detail", "pages/purchase-management/purchase-management", "pages/purchase-detail/purchase-detail", "pages/cost-settings/cost-settings"]}, {"root": "subpackages/banquet", "name": "banquet", "pages": ["pages/banquet-history/banquet-history", "pages/banquet-invitation/banquet-invitation", "pages/banquet-create/banquet-create", "pages/banquet-addcomment/banquet-addcomment", "pages/banquet-resrecommended/banquet-resrecommended", "pages/invite-guests/invite-guests", "pages/banquet-detail/banquet-detail", "pages/banquet-card/banquet-card", "pages/banquet-jinbang/banquet-jinbang", "pages/dish-recognition/dish-recognition", "pages/banquet-resdetail/banquet-resdetail", "pages/banquet-guestdoc/banquet-guestdoc", "pages/banquet-summary/banquet-summary", "pages/my-index/my-index", "pages/my-message/my-message", "pages/my-foodrecord/my-foodrecord", "pages/my-bodyreport/my-bodyreport", "pages/basic-info-preference/basic-info-preference"]}], "preloadRule": {"pages/index/index": {"network": "all", "packages": ["main"]}}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "usingComponents": {"typewriter": "/components/typewriter/typewriter", "custom-tab-bar": "/custom-tab-bar/index", "custom-header": "/components/custom-header/custom-header", "food-recognizer": "/components/food-recognizer/food-recognizer", "dual-slider": "/components/dual-slider/dual-slider", "tag-selector": "/components/tag-selector/tag-selector"}, "resolveAlias": {"@/*": "/*", "@api/*": "/subpackages/main/api/*", "@utils/*": "/utils/*", "@components/*": "/components/*"}, "tabBar": {"custom": true, "color": "#7A7E83", "selectedColor": "#3cc51f", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "image/home/<USER>", "selectedIconPath": "image/home/<USER>", "text": "首页"}, {"pagePath": "pages/ai-chat/ai-chat", "iconPath": "image/home/<USER>", "selectedIconPath": "image/home/<USER>", "text": "餐小参"}, {"pagePath": "pages/user/user", "iconPath": "image/home/<USER>", "selectedIconPath": "image/home/<USER>", "text": "我的"}]}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "智能识别", "navigationBarTextStyle": "black"}, "requiredBackgroundModes": ["audio"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "plugins": {"WechatSI": {"version": "0.3.6", "provider": "wx069ba97219f66d99"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation"]}