/* pages/suggestion-list/suggestion-list.wxss */
.container {
  min-height: 100vh;
  background-color: #F6F6F5;
  padding: 12rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 建议列表外层包裹容器 */
.suggestion-wrapper {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0,0,0,0.01);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  box-sizing: border-box;
  overflow: hidden;
}

/* 建议列表 */
.suggestion-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

.suggestion-item {
  width: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  padding: 24rpx 32rpx 24rpx 32rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
    position: relative;
    transition: all 0.2s;
}

.suggestion-item:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 32rpx;
  right: 32rpx;
  height: 1rpx;
  background-color: #F2F2F2;
}

.suggestion-item:active {
  background-color: #F8F8F8;
}

/* 建议标题 */
.item-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  margin-right: 48rpx;
}

.title-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-status {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
  min-height: 60rpx;
  padding: 12rpx;
  box-sizing: border-box;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.item-status:active {
  background-color: #F0F0F0;
}

/* 建议内容 */
.item-content {
  margin-right: 48rpx;
}

.content-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 底部信息 */
.item-arrow {
  position: absolute;
  top: 50%;
  right: 32rpx;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 742rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0,0,0,0.01);
  border-radius: 16rpx;
  margin: 0 16rpx;
  box-sizing: border-box;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 742rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0,0,0,0.01);
  border-radius: 16rpx;
  margin: 0 16rpx;
  box-sizing: border-box;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部提示 */
.bottom-tip {
  padding: 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
} 