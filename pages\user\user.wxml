<view class="my-pubox">
  <view class="my-user flex">
    <view class="my-toubox flex">
      <image src="{{avatar}}" class="mypic" mode="aspectFit" />
      <view class="my-toutext">
        <view class="my-name f34 bold sgray">{{nickname}}</view>
        <view class="my-toupriz flex mt10">
          <image src="/image/my/r9.png" class="prizeicon" mode="aspectFit" />
          <text class="f24 qgray">{{level_title}}</text>
        </view>
      </view>
    </view>
    <view class="my-chat"><image src="/image/my/r7.png" class="chaticon" mode="aspectFit" /></view>
  </view>

  <!-- quick link -->
  <view class="whitebox my-henlink flex mt30">
    <view class="helink-item flex"><image src="/image/my/r1.png" class="chaticon" mode="aspectFit" /><text class="f24 sgray">个人信息及偏好</text></view>
    <view class="helink-item flex"><image src="/image/my/r2.png" class="chaticon" mode="aspectFit" /><text class="f24 sgray">我的体质报告</text></view>
  </view>
  <view class="whitebox my-shulink mt20">
    <view class="shulink-item flex">
      <view class="shulink-item-lef flex">
        <image src="/image/my/r3.png" class="shuicon" mode="aspectFit" />
        <text class="f24 sgray">个人信息及偏好</text>
      </view>
      <van-icon name="arrow" class="f24 qgray" />
    </view>
    <view class="shulink-item flex">
      <view class="shulink-item-lef flex">
        <image src="/image/my/r4.png" class="shuicon" mode="aspectFit" />
        <text class="f24 sgray">我的饮食记录</text>
      </view>
      <van-icon name="arrow" class="f24 qgray" />
    </view>
    <view class="shulink-item flex">
      <view class="shulink-item-lef flex">
        <image src="/image/my/r5.png" class="shuicon" mode="aspectFit" />
        <text class="f24 sgray">购买记录</text>
      </view>
      <van-icon name="arrow" class="f24 qgray" />
    </view>
  </view>
  <view class="whitebox exitlink f32 bold mt20" bindtap="userLogout">退出登录</view>

</view>