import { getUserinfo } from '@/api/home'
import { clearLoginInfo } from '@utils/auth'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatar:'',
    nickname:'',
    level_title:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.userinfoData();
  },

  userinfoData(){
    wx.showLoading({
      title: '加载中...',
    })
    getUserinfo()
    .then(res =>{
      wx.hideLoading();
      if(res && res.code === 200){
        const { avatar, level_title, nickname, user_key } = res.data;
        console.log(user_key);
        this.setData({
          avatar:avatar,
          nickname:nickname,
          level_title:level_title
        })
      }
    })
    .catch(error => {
      console.log('获取失败：',error)
    })
  },

  // 退出登录
  userLogout(){    
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          clearLoginInfo();
          
          // 跳转到登录页
          wx.reLaunch({
            url: '/subpackages/banquet/pages/my-index/my-index'
          });
          
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})